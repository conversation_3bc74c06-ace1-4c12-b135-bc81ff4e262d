import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/animated_background.dart';
import '../../core/widgets/glass_card.dart';
import '../../core/widgets/gradient_button.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import 'intro_controller.dart';

class IntroView extends GetView<IntroController> {
  const IntroView({super.key});

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final horizontalPadding = isTablet ? 48.0 : 24.0;
    final verticalPadding = isTablet ? 32.0 : 16.0;

    return Scaffold(
      body: AnimatedBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Skip Button
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: horizontalPadding,
                  vertical: verticalPadding,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: controller.skipIntro,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      child: Text(
                        'Skip',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ],
                ),
              ),

              // Page View
              Expanded(
                child: PageView.builder(
                  controller: controller.pageController,
                  itemCount: controller.introItems.length,
                  itemBuilder: (context, index) {
                    final item = controller.introItems[index];
                    return Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: horizontalPadding,
                        vertical: verticalPadding / 2,
                      ),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          final maxWidth = constraints.maxWidth;
                          final contentWidth = isTablet
                              ? (maxWidth * 0.8).clamp(300.0, 500.0)
                              : maxWidth;

                          return Center(
                            child: SizedBox(
                              width: contentWidth,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Icon with Animation
                                  TweenAnimationBuilder<double>(
                                    duration:
                                        AppConstants.longAnimationDuration,
                                    tween: Tween(begin: 0.0, end: 1.0),
                                    builder: (context, value, child) {
                                      return Transform.scale(
                                        scale: value,
                                        child: Container(
                                          width: isTablet ? 140 : 120,
                                          height: isTablet ? 140 : 120,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: LinearGradient(
                                              colors: item.gradient,
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: item.gradient.first
                                                    .withValues(alpha: 0.3),
                                                blurRadius: 20,
                                                offset: const Offset(0, 8),
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            item.icon,
                                            size: isTablet ? 70 : 60,
                                            color: Colors.white,
                                          ),
                                        ),
                                      );
                                    },
                                  ),

                                  SizedBox(height: isTablet ? 64 : 48),

                                  // Content Card
                                  FlatCard(
                                    padding: EdgeInsets.all(isTablet ? 40 : 32),
                                    child: Column(
                                      children: [
                                        Text(
                                          item.title,
                                          style: Theme.of(context)
                                              .textTheme
                                              .headlineSmall
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: AppColors.textPrimary,
                                              ),
                                          textAlign: TextAlign.center,
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          item.description,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyLarge
                                              ?.copyWith(
                                                color: AppColors.textSecondary,
                                                height: 1.5,
                                              ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),

              // Bottom Navigation
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: horizontalPadding,
                  vertical: verticalPadding,
                ),
                child: Column(
                  children: [
                    // Page Indicators
                    Obx(() => Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            controller.introItems.length,
                            (index) => AnimatedContainer(
                              duration: AppConstants.animationDuration,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: controller.currentPage.value == index
                                  ? 24
                                  : 8,
                              height: 8,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: controller.currentPage.value == index
                                    ? Theme.of(context).colorScheme.primary
                                    : AppColors.textLight,
                              ),
                            ),
                          ),
                        )),

                    SizedBox(height: isTablet ? 40 : 32),

                    // Navigation Buttons
                    Row(
                      children: [
                        // Previous Button
                        Obx(() => controller.currentPage.value > 0
                            ? Expanded(
                                child: OutlinedButton(
                                  onPressed: controller.previousPage,
                                  style: OutlinedButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                        vertical: isTablet ? 20 : 16),
                                    side: const BorderSide(
                                        color: Theme.of(context).colorScheme.primary),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius),
                                    ),
                                  ),
                                  child: Text(
                                    'Previous',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          color: Theme.of(context).colorScheme.primary,
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                ),
                              )
                            : const SizedBox.shrink()),

                        if (controller.currentPage.value > 0)
                          const SizedBox(width: 16),

                        // Next/Get Started Button
                        Expanded(
                          flex: controller.currentPage.value > 0 ? 1 : 2,
                          child: Obx(() => GradientButton(
                                text: controller.isLastPage.value
                                    ? 'Get Started'
                                    : 'Next',
                                onPressed: controller.nextPage,
                                gradient: Theme.of(context).auroraGradients.primaryAuroraGradient,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
