import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:google_fonts/google_fonts.dart';

// Aurora Gradient Theme Extension for vivid color schemes
@immutable
class AuroraGradientExtension extends ThemeExtension<AuroraGradientExtension> {
  const AuroraGradientExtension({
    required this.primaryAuroraGradient,
    required this.secondaryAuroraGradient,
    required this.backgroundAuroraGradient,
    required this.accentAuroraGradient,
    required this.surfaceAuroraGradient,
    this.glowColor,
    this.shimmerColors,
  });

  final List<Color> primaryAuroraGradient;
  final List<Color> secondaryAuroraGradient;
  final List<Color> backgroundAuroraGradient;
  final List<Color> accentAuroraGradient;
  final List<Color> surfaceAuroraGradient;
  final Color? glowColor;
  final List<Color>? shimmerColors;

  @override
  AuroraGradientExtension copyWith({
    List<Color>? primaryAuroraGradient,
    List<Color>? secondaryAuroraGradient,
    List<Color>? backgroundAuroraGradient,
    List<Color>? accentAuroraGradient,
    List<Color>? surfaceAuroraGradient,
    Color? glowColor,
    List<Color>? shimmerColors,
  }) {
    return AuroraGradientExtension(
      primaryAuroraGradient:
          primaryAuroraGradient ?? this.primaryAuroraGradient,
      secondaryAuroraGradient:
          secondaryAuroraGradient ?? this.secondaryAuroraGradient,
      backgroundAuroraGradient:
          backgroundAuroraGradient ?? this.backgroundAuroraGradient,
      accentAuroraGradient: accentAuroraGradient ?? this.accentAuroraGradient,
      surfaceAuroraGradient:
          surfaceAuroraGradient ?? this.surfaceAuroraGradient,
      glowColor: glowColor ?? this.glowColor,
      shimmerColors: shimmerColors ?? this.shimmerColors,
    );
  }

  @override
  AuroraGradientExtension lerp(
      ThemeExtension<AuroraGradientExtension>? other, double t) {
    if (other is! AuroraGradientExtension) {
      return this;
    }
    return AuroraGradientExtension(
      primaryAuroraGradient: <Color>[
        Color.lerp(
            primaryAuroraGradient[0], other.primaryAuroraGradient[0], t)!,
        Color.lerp(
            primaryAuroraGradient[1], other.primaryAuroraGradient[1], t)!,
        if (primaryAuroraGradient.length > 2)
          Color.lerp(
              primaryAuroraGradient[2],
              other.primaryAuroraGradient.length > 2
                  ? other.primaryAuroraGradient[2]
                  : primaryAuroraGradient[2],
              t)!,
      ],
      secondaryAuroraGradient: <Color>[
        Color.lerp(
            secondaryAuroraGradient[0], other.secondaryAuroraGradient[0], t)!,
        Color.lerp(
            secondaryAuroraGradient[1], other.secondaryAuroraGradient[1], t)!,
        if (secondaryAuroraGradient.length > 2)
          Color.lerp(
              secondaryAuroraGradient[2],
              other.secondaryAuroraGradient.length > 2
                  ? other.secondaryAuroraGradient[2]
                  : secondaryAuroraGradient[2],
              t)!,
      ],
      backgroundAuroraGradient: <Color>[
        Color.lerp(
            backgroundAuroraGradient[0], other.backgroundAuroraGradient[0], t)!,
        Color.lerp(
            backgroundAuroraGradient[1], other.backgroundAuroraGradient[1], t)!,
        if (backgroundAuroraGradient.length > 2)
          Color.lerp(
              backgroundAuroraGradient[2],
              other.backgroundAuroraGradient.length > 2
                  ? other.backgroundAuroraGradient[2]
                  : backgroundAuroraGradient[2],
              t)!,
      ],
      accentAuroraGradient: <Color>[
        Color.lerp(accentAuroraGradient[0], other.accentAuroraGradient[0], t)!,
        Color.lerp(accentAuroraGradient[1], other.accentAuroraGradient[1], t)!,
      ],
      surfaceAuroraGradient: <Color>[
        Color.lerp(
            surfaceAuroraGradient[0], other.surfaceAuroraGradient[0], t)!,
        Color.lerp(
            surfaceAuroraGradient[1], other.surfaceAuroraGradient[1], t)!,
      ],
      glowColor: Color.lerp(glowColor, other.glowColor, t),
      shimmerColors: shimmerColors,
    );
  }

  // Aurora Gradient Themes for different schemes
  static const AuroraGradientExtension mandyRedLight = AuroraGradientExtension(
    primaryAuroraGradient: <Color>[
      Color(0xFFFF6B9D),
      Color(0xFFFF8E9B),
      Color(0xFFFFA8A8)
    ],
    secondaryAuroraGradient: <Color>[Color(0xFFFFB4B4), Color(0xFFFFD6CC)],
    backgroundAuroraGradient: <Color>[
      Color(0xFFFFF5F5),
      Color(0xFFFFE8E8),
      Color(0xFFFFD6D6)
    ],
    accentAuroraGradient: <Color>[Color(0xFFFF9A9E), Color(0xFFFAD0C4)],
    surfaceAuroraGradient: <Color>[Color(0xFFFFFAFA), Color(0xFFFFF0F0)],
    glowColor: Color(0xFFFF6B9D),
    shimmerColors: <Color>[
      Color(0xFFFFE8E8),
      Color(0xFFFFD6D6),
      Color(0xFFFFB4B4)
    ],
  );

  static const AuroraGradientExtension mandyRedDark = AuroraGradientExtension(
    primaryAuroraGradient: <Color>[
      Color(0xFFFF6B9D),
      Color(0xFFFF4757),
      Color(0xFFE84393)
    ],
    secondaryAuroraGradient: <Color>[Color(0xFF6C5CE7), Color(0xFFA29BFE)],
    backgroundAuroraGradient: <Color>[
      Color(0xFF2D3436),
      Color(0xFF636E72),
      Color(0xFF74B9FF)
    ],
    accentAuroraGradient: <Color>[Color(0xFFFF7675), Color(0xFFE17055)],
    surfaceAuroraGradient: <Color>[Color(0xFF2D3436), Color(0xFF636E72)],
    glowColor: Color(0xFFFF6B9D),
    shimmerColors: <Color>[
      Color(0xFF6C5CE7),
      Color(0xFFA29BFE),
      Color(0xFF74B9FF)
    ],
  );
}

// Available Aurora Theme Schemes
enum AuroraThemeScheme {
  mandyRed,
  oceanBlue,
  forestGreen,
  sunsetOrange,
  purpleNight,
  cosmicPink,
}

class AppTheme {
  // Get theme data for specific Aurora scheme
  static ThemeData getTheme(AuroraThemeScheme scheme, bool isDark) {
    switch (scheme) {
      case AuroraThemeScheme.mandyRed:
        return _buildMandyRedTheme(isDark);
      case AuroraThemeScheme.oceanBlue:
        return _buildOceanBlueTheme(isDark);
      case AuroraThemeScheme.forestGreen:
        return _buildForestGreenTheme(isDark);
      case AuroraThemeScheme.sunsetOrange:
        return _buildSunsetOrangeTheme(isDark);
      case AuroraThemeScheme.purpleNight:
        return _buildPurpleNightTheme(isDark);
      case AuroraThemeScheme.cosmicPink:
        return _buildCosmicPinkTheme(isDark);
    }
  }

  // Mandy Red Theme (Default)
  static ThemeData _buildMandyRedTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.mandyRed,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
      extensions: <ThemeExtension<dynamic>>[
        isDark
            ? AuroraGradientExtension.mandyRedDark
            : AuroraGradientExtension.mandyRedLight,
      ],
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Ocean Blue Theme
  static ThemeData _buildOceanBlueTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.blue,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Forest Green Theme
  static ThemeData _buildForestGreenTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.green,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Sunset Orange Theme
  static ThemeData _buildSunsetOrangeTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.amber,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Purple Night Theme
  static ThemeData _buildPurpleNightTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.purpleBrown,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Cosmic Pink Theme
  static ThemeData _buildCosmicPinkTheme(bool isDark) {
    return FlexThemeData.light(
      scheme: FlexScheme.sakura,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
    ).copyWith(
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }

  // Default themes for backward compatibility
  static ThemeData get lightTheme {
    return getTheme(AuroraThemeScheme.mandyRed, false);
  }

  static ThemeData get darkTheme {
    return getTheme(AuroraThemeScheme.mandyRed, true);
  }

  // Get theme name for display
  static String getThemeName(AuroraThemeScheme scheme) {
    switch (scheme) {
      case AuroraThemeScheme.mandyRed:
        return 'Mandy Red';
      case AuroraThemeScheme.oceanBlue:
        return 'Ocean Blue';
      case AuroraThemeScheme.forestGreen:
        return 'Forest Green';
      case AuroraThemeScheme.sunsetOrange:
        return 'Sunset Orange';
      case AuroraThemeScheme.purpleNight:
        return 'Purple Night';
      case AuroraThemeScheme.cosmicPink:
        return 'Cosmic Pink';
    }
  }
}

// Extension to easily access Aurora gradients from theme
extension ThemeDataExtensions on ThemeData {
  AuroraGradientExtension get auroraGradients =>
      extension<AuroraGradientExtension>()!;
}
